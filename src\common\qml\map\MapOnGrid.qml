import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts
import QtLocation
import QtPositioning
import models 1.0

Rectangle {
    id: mapOnGrid

    property bool isLongPress: false
    anchors.fill: parent
    property var cameraList: []
    property int previewWidth: 500
    property int previewHeight: 340
    property var thisMapModel: mapModel ? mapModel : null
    property var thisMapState: mapState ? mapState : null

    property string prevItem: ""
    property string curItem: "1"

    property var currentItem: null
    property bool previewItemHovering: false
    property bool currentItemHovering: false

    signal previewingItemChanged()

    onCurItemChanged: {
        if (prevItem !== curItem && curItem !== "") {
            prevItem = curItem
            previewingItemChanged()
        }
    }

    onPreviewingItemChanged: {
        openPreviewTimer.stop()
        if (curItem !== "") {
            openPreviewTimer.start()
        }
    }

    property var mainGrid: {
        if(thisMapState && thisMapState.editMode) return null;
        var current = mapOnGrid.parent
        while (current) {
            if (current.objectName === "MainGrid" || current.toString().indexOf("MainGrid") !== -1) {
                return current
            }
            current = current.parent
        }
        return null
    }

    Loader {
        id: cameraDetailLoader
        width: previewWidth
        height: previewHeight
        visible: sourceComponent !== null
        active: thisMapState ? thisMapState.editMode : false
        z: 1000
        parent: mainGrid ? mainGrid : mapOnGrid
    }

    GeocodeModel {
        id: geocodeModel
        plugin: map.plugin
        // đợi khi có query mới bắt đầu tìm
        query: ""
        // chỉ lấy tối đa 5 kết quả
        limit: 5
    }

    DropArea {
        anchors.fill: parent
        enabled: thisMapState ? thisMapState.editMode && !thisMapState.lockMode : false // chỉ nhận drop ở chế độ chỉnh sửa map và trạng thái là unlock
        onDropped: function(drop) {
            if (thisMapState.editMode) {
                if (gridModel) gridModel.isSave = false
                let coord = map.toCoordinate(Qt.point(drop.x, drop.y))
                thisMapModel.handleDrop(drop.getDataAsArrayBuffer("application/json"),coord.latitude,coord.longitude)
            } else {
                thisMapState.notifyChanged(thisMapState.SelectEditMode)
            }

        }
    }
    
    Map {
        id: map
        anchors.fill: parent
        plugin: Plugin {
            id: mapPlugin
            name: "osm"
            PluginParameter {
                name: "osm.mapping.custom.host"
                value: "https://api.gpstech.vn/geo-service/styles/basic-preview/%z/%x/%y.png?access_token=" + (thisMapModel.accessToken ? thisMapModel.accessToken : "")
            }
            PluginParameter {
                name: "osm.geocoding.host"
                value: "https://api.gpstech.vn/geo-service/"
            }
            // PluginParameter {
            //     name: "osm.mapping.offline.directory"
            //     value: "D:/GEOSERVER"
            // }
            // PluginParameter {
            //     name: "osm.mapping.cache.directory"
            //     value: "D:/GEOSERVER"
            // }
            PluginParameter {
                name: "osm.cache.directory"
                value: ""
            }
            PluginParameter {
                name: "osm.cache.disk.cost"
                value: "0"
            }
            PluginParameter {
                name: "osm.cache.memory.cost"
                value: "0"
            }
        }

        zoomLevel: 14
        center: QtPositioning.coordinate(21.014506, 105.846509)
        activeMapType: supportedMapTypes[supportedMapTypes.length - 1]
        property geoCoordinate startCentroid
        antialiasing: true

        // Function to handle map movement
        function handleMapMove(dx, dy) {
            if (mapOnGrid.isMapMoveEnabled) {
                map.pan(-dx, -dy)
            }
        }

        MapItemView {
            id: mapItemView
            model: (function(){
                    return thisMapModel.cameraIds
                })()
            delegate: MapItem {
                id: cameraItem
                model: (function(){
                    return modelData
                })()
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                rtsp: modelData.urlMainstream
                itemId: modelData.id
                itemName: modelData.name
                camLocation: modelData.location
                onUntrackSignal: (cameraModel) => {
                    if (gridModel) gridModel.isSave = false
                    thisMapModel.removeCameraFromMap(cameraModel)
                }
                onButtonSignal: () => {
                    if (thisMapState.editMode){
                        handleShowDialogEditMode(cameraItem, previewItemComponent)
                    }
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: cameraItem.model
                        buttonType: "Camera"
                        isViewMode: thisMapState ? !thisMapState.editMode : false
                        isPlayingStream: true
                        visible: true
                        itemName: cameraItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            thisMapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            if (thisMapState.editMode){
                                handleDialogPositionEditMode(cameraItem)
                            }
                        }
                        onHoverStateChanged: function (isHovering) {
                            previewItemHovering = isHovering
                            if(!isHovering){
                                hoverTimer.start()
                            }
                            else hoverTimer.stop()
                        }
                    }
                }

                onIsItemHoveredChanged: {
                    if(!thisMapState.editMode){
                        openPreviewTimer.stop()
                        hoverTimer.stop()

                        if (isItemHovered) {
                            handlePositionDialogViewMode(cameraItem, previewItemComponent)
                            curItem = cameraItem.itemId
                            currentItemHovering = true
                            openPreviewTimer.start()
                        } else {
                            // Only reset if this was the current item being hovered
                            if (curItem === cameraItem.itemId) {
                                curItem = ""
                                currentItemHovering = false
                                hoverTimer.start()
                            }
                        }
                    }
                }

            }
        }
        MapItemView {
            model: thisMapModel.buildingIds
            delegate: MapItem {
                id: buildingItem
                model: modelData
                itemType: "BuildingItem"
                itemId: modelData.id
                itemName: modelData.name
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                onUntrackSignal: (buildingModel) => {
                    if (gridModel) gridModel.isSave = false
                    thisMapModel.removeBuildingFromMap(buildingModel)
                }
                onButtonSignal: () => {
                    if (thisMapState.editMode){
                        handleShowDialogEditMode(buildingItem, previewItemComponent)
                    }
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: buildingItem.model
                        buttonType: "Building"
                        isViewMode: thisMapState ? !thisMapState.editMode : false
                        visible: true
                        itemName: buildingItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            thisMapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            if (thisMapState.editMode){
                                handleDialogPositionEditMode(buildingItem)
                            }
                        }
                        Component.onCompleted:{
                            // Component initialization
                        }

                        onHoverStateChanged: function (isHovering) {
                            previewItemHovering = isHovering
                            if(!isHovering){
                                hoverTimer.start()
                            }
                            else hoverTimer.stop()
                        }
                    }
                }

                onIsItemHoveredChanged: {
                    if(!thisMapState.editMode){
                        openPreviewTimer.stop()
                        hoverTimer.stop()

                        if (isItemHovered) {
                            handlePositionDialogViewMode(buildingItem, previewItemComponent)
                            curItem = buildingItem.itemId
                            currentItemHovering = true
                            openPreviewTimer.start()
                        } else {
                            // Only reset if this was the current item being hovered
                            if (curItem === buildingItem.itemId) {
                                curItem = ""
                                currentItemHovering = false
                                hoverTimer.start()
                            }
                        }
                    }
                }
            }
        }

        Timer {
            id: hoverTimer
            interval: 200
            repeat: false
            onTriggered: {
                // console.log("hoverTimer triggered", previewItemHovering, currentItemHovering, thisMapState.editMode)
                if (!previewItemHovering && !currentItemHovering && !thisMapState.editMode) {
                    cameraDetailLoader.active = false
                    cameraDetailLoader.sourceComponent = null
                }
            }
        }

        Timer {
            id: openPreviewTimer
            interval: 500
            repeat: false
            onTriggered: {
                if(!thisMapState.editMode){
                    cameraDetailLoader.active = true;
                }
            }
        }
    

        Behavior on center {
            PropertyAnimation {
                duration: 400
                easing.type: Easing.InOutQuad
            }
        }

        PinchHandler {
            id: pinch
            enabled: thisMapState ? !thisMapState.lockMode : false
            target: null
            onActiveChanged: if (active) {
                map.startCentroid = map.toCoordinate(pinch.centroid.position, false)
            }
            onScaleChanged: (delta) => {
                map.zoomLevel += Math.log2(delta)
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            onRotationChanged: (delta) => {
                map.bearing -= delta
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            grabPermissions: PointerHandler.TakeOverForbidden
        }
        WheelHandler {
            id: wheel
            enabled: thisMapState ? !thisMapState.lockMode : false
            grabPermissions: PointerHandler.ApprovesCancellation
            acceptedDevices: Qt.platform.pluginName === "cocoa" || Qt.platform.pluginName === "wayland"
                             ? PointerDevice.Mouse | PointerDevice.TouchPad
                             : PointerDevice.Mouse
            rotationScale: 1 /120
            property: "zoomLevel"
        }
        DragHandler {
            id: drag
            target: null
            grabPermissions: PointerHandler.TakeOverForbidden
            enabled: mapOnGrid.isMapMoveEnabled && (thisMapState ? !thisMapState.lockMode : true)
            onTranslationChanged: (delta) => map.pan(-delta.x, -delta.y)
            // Make sure this handler doesn't interfere with hover events
            acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad
        }


        Shortcut {
            enabled: map.zoomLevel < map.maximumZoomLevel
            sequence: StandardKey.ZoomIn
            onActivated: map.zoomLevel = Math.round(map.zoomLevel + 1)
        }
        Shortcut {
            enabled: map.zoomLevel > map.minimumZoomLevel
            sequence: StandardKey.ZoomOut
            onActivated: map.zoomLevel = Math.round(map.zoomLevel - 1)
        }
    }

    Loader {
        id: searchBarLoader
        anchors.left: mapOnGrid.left
        anchors.top: mapOnGrid.top
        anchors.margins: 10
        z: 2000
        active: thisMapState ? !thisMapState.editMode && !thisMapState.lockMode : false

        sourceComponent: Rectangle {
            id: searchContainer
            width: 300
            height: searchColumn.height
            radius: 8
            color: "white"
            border.color: "#E0E0E0"
            border.width: 1

            property alias searchText: searchField.text
            property alias suggestionsList: suggestionsList

            // Drop shadow effect
            Rectangle {
                anchors.fill: parent
                anchors.topMargin: 2
                anchors.leftMargin: 2
                radius: parent.radius
                color: "#20000000"
                z: -1
            }

            Column {
                id: searchColumn
                width: parent.width
                spacing: 0

                Rectangle {
                    id: searchInputContainer
                    width: parent.width
                    height: 40
                    radius: 8
                    color: "transparent"

                    Row {
                        anchors.fill: parent
                        anchors.margins: 8
                        spacing: 8

                        Text {
                            text: "\u{1F50D}" // Search icon
                            font.pixelSize: 16
                            color: "#666666"
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        TextField {
                            id: searchField
                            width: parent.width - 32
                            height: parent.height
                            placeholderText: qsTr("Search address...")
                            placeholderTextColor: "#999999"
                            font.pixelSize: 14
                            background: null
                            color: "#333333"

                            onTextChanged: {
                                searchTimer.restart()
                                if (text.length === 0) {
                                    suggestionsList.visible = false
                                    geocodeModel.query = ""
                                }
                            }

                            onActiveFocusChanged: {
                                if (!activeFocus && suggestionsList.visible) {
                                    // Delay hiding to allow for item selection
                                    hideTimer.start()
                                }
                            }
                        }
                    }
                }

                Rectangle {
                    id: suggestionsContainer
                    width: parent.width
                    height: Math.min(suggestionsList.contentHeight, 200)
                    visible: suggestionsList.visible
                    color: "white"
                    border.color: "#E0E0E0"
                    border.width: 1

                    // Block mouse events from reaching map
                    MouseArea {
                        anchors.fill: parent
                        acceptedButtons: Qt.AllButtons
                        hoverEnabled: true
                        onPressed: function(mouse) {
                            mouse.accepted = true
                        }
                        onWheel: function(wheel) {
                            wheel.accepted = true
                        }
                    }

                    ListView {
                        id: suggestionsList
                        anchors.fill: parent
                        visible: false
                        clip: true

                        model: geocodeModel

                    delegate: ItemDelegate {
                        width: suggestionsList.width
                        height: 40

                        Rectangle {
                            anchors.fill: parent
                            color: parent.hovered ? "#F5F5F5" : "transparent"

                            Text {
                                anchors.left: parent.left
                                anchors.leftMargin: 12
                                anchors.verticalCenter: parent.verticalCenter
                                text: locationData.address.text
                                font.pixelSize: 13
                                color: "#333333"
                                elide: Text.ElideRight
                                width: parent.width - 24
                            }
                        }

                        onClicked: {
                            searchField.text = locationData.address.text
                            suggestionsList.visible = false

                            // Move map to selected location
                            if (locationData.coordinate.isValid) {
                                map.center = locationData.coordinate
                                map.zoomLevel = Math.max(map.zoomLevel, 15)
                            }
                        }
                    }
                }
                }
            }

            // Block wheel events only, allow clicks for focus
            MouseArea {
                anchors.fill: parent
                acceptedButtons: Qt.NoButton // Don't accept click events
                hoverEnabled: true
                onWheel: function(wheel) {
                    wheel.accepted = true // Block wheel events from reaching map
                }
                onContainsMouseChanged: {
                    if (containsMouse) {
                        hideTimer.stop()
                    }
                }
            }
        }
    }

    // Timer for search debouncing
    Timer {
        id: searchTimer
        interval: 500
        repeat: false
        onTriggered: {
            if (searchBarLoader.item && searchBarLoader.item.searchText.length > 2) {
                geocodeModel.query = searchBarLoader.item.searchText
                if (searchBarLoader.item.suggestionsList) {
                    searchBarLoader.item.suggestionsList.visible = true
                }
            }
        }
    }

    // Timer for hiding suggestions
    Timer {
        id: hideTimer
        interval: 200
        repeat: false
        onTriggered: {
            if (searchBarLoader.item && searchBarLoader.item.suggestionsList) {
                searchBarLoader.item.suggestionsList.visible = false
            }
        }
    }

    // Handle geocode model status changes
    Connections {
        target: geocodeModel
        function onStatusChanged() {
            if (geocodeModel.status === GeocodeModel.Ready && searchBarLoader.item && searchBarLoader.item.suggestionsList) {
                if (geocodeModel.count > 0) {
                    searchBarLoader.item.suggestionsList.visible = true
                } else {
                    searchBarLoader.item.suggestionsList.visible = false
                }
            }
        }
    }

    // DragHandler {
    //     id: dragHandler
    //     enabled: (thisMapState && thisMapState.lockMode) ? true : false
    //     onActiveChanged: {
    //         if (active) {
    //             root.currentMimeData = {
    //                 "text/plain": "swap_item",
    //                 "application/position": JSON.stringify({
    //                     id: mapModel.id,
    //                     tree_type: "",
    //                     position: widget.getPosition()
    //                 })
    //             }
    //         }
    //     }
    // }


    // Drag.source: root
    // Drag.active: dragHandler.active
    // Drag.mimeData: root.currentMimeData
    // // Drag.mimeData: {
    // //     "text/plain": "swap_item",
    // //     "application/position": JSON.stringify({
    // //         id: mapModel.id,
    // //         tree_type: "",
    // //         position: widget.getPosition()
    // //     })
    // // }

    // Drag.dragType: Drag.Automatic
    // Drag.supportedActions: Qt.MoveAction

    Connections {
        target: thisMapModel
        function onNewCameraChanged(camera) {
            cameraList.push(camera)
        }

        
    }
    Connections {
        target: thisMapState
        function onLockModeChanged(){
            if(thisMapState.lockMode){
                cameraDetailLoader.sourceComponent = null;
            }
        }
    }

    // Add function to handle map movement from parent
    function handleMapMove(dx, dy) {
        map.handleMapMove(dx, dy)
    }

    // Add property to control map movement mode
    property bool isMapMoveEnabled: true

    // Add signal to notify parent of map movement mode changes
    signal mapMoveModeChanged(bool enabled)

    // Update map movement mode
    function setMapMoveMode(enabled) {
        isMapMoveEnabled = enabled
        // The drag handler's enabled property is bound to the condition, so no need to set it directly
        // Also update the map's internal property
        map.isMapMoveEnabled = enabled
        mapMoveModeChanged(enabled)
    }

    function handleDialogPositionEditMode(item){
        thisMapState.viewMode = !thisMapState.viewMode
        if (thisMapState.viewMode){
            cameraDetailLoader.width = map.width
            cameraDetailLoader.height = map.height
            cameraDetailLoader.x = 0
            cameraDetailLoader.y = 0
        }else{
            cameraDetailLoader.width = previewWidth
            cameraDetailLoader.height = previewHeight
            if (item.x < 0) {
                cameraDetailLoader.x = 0
            }else{
                if ((item.x + previewWidth + 30) > map.width){
                    cameraDetailLoader.x = map.width - previewWidth - 30
                }else{
                    cameraDetailLoader.x = item.x + 30;
                }
            }
            if (item.y < 0) {
                cameraDetailLoader.y = 0
            }else {
                if ((item.y + previewHeight + 30) > map.height){
                    cameraDetailLoader.y = map.height - previewHeight - 30
                }else{
                    cameraDetailLoader.y = item.y + 30;
                }
            }
        }
    }

    function handleShowDialogEditMode(item, previewItemComponent){
        if (item.x < 0) {
            cameraDetailLoader.x = 0
        }else{
            if ((item.x + previewWidth + 30) > map.width){
                cameraDetailLoader.x = map.width - previewWidth - 30
            }else{
                cameraDetailLoader.x = item.x + 30;
            }
        }
        if (item.y < 0) {
            cameraDetailLoader.y = 0
        }else {
            if ((item.y + previewHeight + 30) > map.height){
                cameraDetailLoader.y = map.height - previewHeight - 30
            }else{
                cameraDetailLoader.y = item.y + 30;
            }
        }
        cameraDetailLoader.sourceComponent = previewItemComponent
    }

    function handlePositionDialogViewMode(item, previewItemComponent){
        cameraDetailLoader.active = false;
        var defaultMapX = item.x + (item.width/2) - (previewWidth/2);
        var defaultMapY = item.y + item.height + 10;

        var pt = mapOnGrid.mapToItem(mainGrid, Qt.point(defaultMapX, defaultMapY));

        var clampedX = Math.max(0, Math.min(pt.x, mainGrid.width - previewWidth));
        var clampedY = Math.max(0, Math.min(pt.y, mainGrid.height - previewHeight));

        cameraDetailLoader.x = clampedX;
        cameraDetailLoader.y = clampedY;
        cameraDetailLoader.sourceComponent = previewItemComponent;
    }
}
